<template>
  <view class="login-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <view class="logo-circle">
        <text class="logo-text">甩单</text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-section">
      <!-- 手机号快捷登录按钮 -->
      <button 
        class="login-btn primary-btn" 
        @click="handlePhoneLogin"
        :loading="isLoading"
      >
        手机号快捷登录
      </button>

      <!-- 暂不登录按钮 -->
      <button 
        class="login-btn secondary-btn" 
        @click="handleSkipLogin"
      >
        暂不登录
      </button>
    </view>

    <!-- 服务条款 -->
    <view class="terms-section">
      <checkbox 
        :checked="agreedToTerms" 
        @change="handleTermsChange"
        class="terms-checkbox"
      />
      <text class="terms-text">
        登录即视为同意 
        <text class="terms-link" @click="showServiceTerms">《服务条款》</text>
        和 
        <text class="terms-link" @click="showPrivacyPolicy">《接单隐私政策》</text>
      </text>
    </view>
  </view>
</template>

<script>
import { authAPI, utils } from '@/utils/api.js'

export default {
  data() {
    return {
      isLoading: false,
      agreedToTerms: false
    }
  },
  
  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus()
  },

  methods: {
    /**
     * 检查登录状态
     */
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (token) {
        // 已登录，直接跳转到消息页面
        this.navigateToChat()
      }
    },

    /**
     * 处理手机号登录
     */
    async handlePhoneLogin() {
      if (!this.agreedToTerms) {
        uni.showToast({
          title: '请先同意服务条款',
          icon: 'none'
        })
        return
      }

      this.isLoading = true
      
      try {
        // 获取微信登录code
        const loginRes = await this.getWechatLoginCode()
        
        // 调用后端登录接口
        const loginResult = await this.callLoginAPI(loginRes.code)
        
        if (loginResult.success) {
          // 保存token和用户信息
          uni.setStorageSync('token', loginResult.data.token)
          uni.setStorageSync('userInfo', loginResult.data.user)
          
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 跳转到消息页面
          setTimeout(() => {
            this.navigateToChat()
          }, 1500)
        } else {
          throw new Error(loginResult.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: error.message || '登录失败，请重试',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 获取微信登录code
     */
    getWechatLoginCode() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: (res) => {
            if (res.code) {
              resolve(res)
            } else {
              reject(new Error('获取登录code失败'))
            }
          },
          fail: (err) => {
            reject(new Error('微信登录失败'))
          }
        })
      })
    },

    /**
     * 调用后端登录API
     */
    async callLoginAPI(code) {
      return await authAPI.login(code)
    },

    /**
     * 处理暂不登录
     */
    handleSkipLogin() {
      // 清除可能存在的登录信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 跳转到消息页面（游客模式）
      this.navigateToChat()
    },

    /**
     * 跳转到消息页面
     */
    navigateToChat() {
      uni.redirectTo({
        url: '/pages/chat/chat'
      })
    },

    /**
     * 处理服务条款勾选
     */
    handleTermsChange(e) {
      this.agreedToTerms = e.detail.value.length > 0
    },

    /**
     * 显示服务条款
     */
    showServiceTerms() {
      uni.showModal({
        title: '服务条款',
        content: '这里是服务条款的内容...',
        showCancel: false
      })
    },

    /**
     * 显示隐私政策
     */
    showPrivacyPolicy() {
      uni.showModal({
        title: '接单隐私政策',
        content: '这里是隐私政策的内容...',
        showCancel: false
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 0 60rpx;
}

.logo-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 200rpx;
}

.logo-circle {
  width: 200rpx;
  height: 200rpx;
  background-color: #22c55e;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-text {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
}

.button-section {
  margin-bottom: 100rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  margin-bottom: 32rpx;
  border: none;
}

.primary-btn {
  background-color: #22c55e;
  color: white;
}

.primary-btn:active {
  background-color: #16a34a;
}

.secondary-btn {
  background-color: transparent;
  color: #22c55e;
  border: 2rpx solid #22c55e;
}

.secondary-btn:active {
  background-color: #f0fdf4;
}

.terms-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 60rpx;
  padding: 0 20rpx;
}

.terms-checkbox {
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.terms-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.terms-link {
  color: #22c55e;
  text-decoration: underline;
}
</style>
